import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Wallet,
    ShoppingBag,
    ArrowRight,
    Signal,
    Globe,
    Zap,
    TrendingUp,
    ChevronRight,
    ChevronLeft,
    BarChart3,
    LineChart as LineChartIcon,
    Activity,
    Download,
    Maximize2,
    RefreshCw,
    DollarSign,
    Calendar,
    Filter
} from 'lucide-react';
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    Area,
    AreaChart,
    Bar<PERSON>hart,
    Bar,
    Composed<PERSON><PERSON>
} from 'recharts';
import { motion, AnimatePresence } from 'framer-motion';
import api from '@/lib/axios';

// Enhanced StatCard with modern design
const StatCard = ({ title, value, icon: Icon, trend, onClick, loading, description, color }) => {
    // Define gradient colors for each card type
    const gradients = {
        purple: 'from-purple-500 to-indigo-600',
        blue: 'from-blue-500 to-cyan-600',
        green: 'from-emerald-500 to-teal-600',
        orange: 'from-orange-500 to-amber-600',
    };

    const selectedGradient = gradients[color] || gradients.blue;

    return (
        <Card className="overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer">
            <div className={`bg-gradient-to-br ${selectedGradient} p-6 text-white h-full`} onClick={onClick}>
                <div className="flex items-start justify-between">
                    <div>
                        <p className="text-sm font-medium text-white/90">{title}</p>
                        {loading ? (
                            <Skeleton className="h-10 w-28 mt-2 bg-white/30" />
                        ) : (
                            <>
                                <h3 className="text-3xl font-bold mt-2">{value}</h3>
                                {description && (
                                    <p className="text-sm text-white/80 mt-1">{description}</p>
                                )}
                                {trend && (
                                    <div className="flex items-center mt-3 text-white/90">
                                        {trend.type === 'up' ? (
                                            <TrendingUp className="w-4 h-4 mr-1" />
                                        ) : (
                                            <TrendingUp className="w-4 h-4 mr-1 transform rotate-180" />
                                        )}
                                        <p className="text-sm font-medium">{trend.value}</p>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                    <div className="bg-white/20 p-3 rounded-xl backdrop-blur-sm">
                        <Icon className="w-6 h-6" />
                    </div>
                </div>
            </div>
        </Card>
    );
};

export default function Dashboard() {
    const navigate = useNavigate();
    const { toast } = useToast();
    const [loading, setLoading] = useState(true);
    const [chartType, setChartType] = useState('area'); // 'area', 'line', 'bar', 'composed'
    const [timePeriod, setTimePeriod] = useState('6months'); // '3months', '6months', '1year'
    const [isChartFullscreen, setIsChartFullscreen] = useState(false);
    const [stats, setStats] = useState({
        walletBalance: 0,
        totalOrders: 0,
        totalPlans: 0,
        last30DaysOrders: 0,
        monthlyTrends: [],
        recentOrders: [],
        totalRevenue: 0,
        monthlyRevenue: 0,
        averageOrderValue: 0,
        growthRate: 0
    });

    useEffect(() => {
        fetchDashboardStats();
    }, [timePeriod]);

    const fetchDashboardStats = async () => {
        try {
            setLoading(true);
            const response = await api.get(`/api/partner/dashboard/stats?period=${timePeriod}`);
            setStats(response.data);
        } catch (error) {
            // console.error('Error fetching dashboard stats:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch dashboard statistics"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleRefreshData = () => {
        fetchDashboardStats();
    };

    const handleExportChart = () => {
        // Implementation for chart export
        toast({
            title: "Export Started",
            description: "Chart data is being exported..."
        });
    };

    return (
        <div className="h-full flex flex-col gap-6 bg-gradient-to-br from-slate-50 to-gray-100 p-6 rounded-lg">
            <div className="bg-gradient-to-r from-blue-800 to-blue-600 p-6 rounded-xl shadow-sm">
                <h1 className="text-2xl font-bold text-white">Partner Dashboard</h1>
                <p className="text-slate-600 mt-1 text-white">Welcome back! Here's an overview of your eSIM services</p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                    title="Wallet Balance"
                    value={`$${stats.walletBalance.toFixed(2)}`}
                    icon={Wallet}
                    description="Available funds for purchases"
                    onClick={() => navigate('/dashboard/wallet')}
                    loading={loading}
                    color="purple"
                />
                <StatCard
                    title="Total Orders"
                    value={stats.totalOrders}
                    icon={ShoppingBag}
                    description="Lifetime eSIM purchases"
                    trend={{
                        type: 'up',
                        value: `${stats.last30DaysOrders} new this month`
                    }}
                    onClick={() => navigate('/dashboard/orders')}
                    loading={loading}
                    color="blue"
                />
                <StatCard
                    title="Total Revenue"
                    value={`$${stats.totalRevenue?.toFixed(2) || '0.00'}`}
                    icon={DollarSign}
                    description="Lifetime earnings"
                    trend={{
                        type: stats.growthRate >= 0 ? 'up' : 'down',
                        value: `${Math.abs(stats.growthRate || 0).toFixed(1)}% growth`
                    }}
                    onClick={() => navigate('/dashboard/orders')}
                    loading={loading}
                    color="green"
                />
                <StatCard
                    title="Avg Order Value"
                    value={`$${stats.averageOrderValue?.toFixed(2) || '0.00'}`}
                    icon={Activity}
                    description="Average per order"
                    onClick={() => navigate('/dashboard/orders')}
                    loading={loading}
                    color="orange"
                />
            </div>

            {/* Enhanced Order History Chart */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
            >
                <Card className="shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden bg-white/60 backdrop-blur-sm border-0">
                    <div className="bg-gradient-to-r from-slate-800 to-blue-800 p-6 border-b">
                        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                            <div>
                                <h2 className="text-xl font-bold text-white">Order & Revenue Analytics</h2>
                                <p className="text-sm text-blue-100">Track your eSIM sales performance over time</p>
                            </div>

                            <div className="flex flex-wrap items-center gap-3">
                                {/* Time Period Filter */}
                                <Select value={timePeriod} onValueChange={setTimePeriod}>
                                    <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white">
                                        <Calendar className="w-4 h-4 mr-2" />
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="3months">3 Months</SelectItem>
                                        <SelectItem value="6months">6 Months</SelectItem>
                                        <SelectItem value="1year">1 Year</SelectItem>
                                    </SelectContent>
                                </Select>

                                {/* Chart Type Buttons */}
                                <div className="flex bg-white/10 rounded-lg p-1">
                                    <Button
                                        variant={chartType === 'area' ? 'default' : 'ghost'}
                                        size="sm"
                                        className={`h-8 px-3 ${chartType === 'area' ? 'bg-white text-slate-800' : 'text-white hover:bg-white/20'}`}
                                        onClick={() => setChartType('area')}
                                    >
                                        <Activity className="w-3 h-3 mr-1" />
                                        Area
                                    </Button>
                                    <Button
                                        variant={chartType === 'line' ? 'default' : 'ghost'}
                                        size="sm"
                                        className={`h-8 px-3 ${chartType === 'line' ? 'bg-white text-slate-800' : 'text-white hover:bg-white/20'}`}
                                        onClick={() => setChartType('line')}
                                    >
                                        <LineChartIcon className="w-3 h-3 mr-1" />
                                        Line
                                    </Button>
                                    <Button
                                        variant={chartType === 'bar' ? 'default' : 'ghost'}
                                        size="sm"
                                        className={`h-8 px-3 ${chartType === 'bar' ? 'bg-white text-slate-800' : 'text-white hover:bg-white/20'}`}
                                        onClick={() => setChartType('bar')}
                                    >
                                        <BarChart3 className="w-3 h-3 mr-1" />
                                        Bar
                                    </Button>
                                    <Button
                                        variant={chartType === 'composed' ? 'default' : 'ghost'}
                                        size="sm"
                                        className={`h-8 px-3 ${chartType === 'composed' ? 'bg-white text-slate-800' : 'text-white hover:bg-white/20'}`}
                                        onClick={() => setChartType('composed')}
                                    >
                                        <TrendingUp className="w-3 h-3 mr-1" />
                                        Mixed
                                    </Button>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex gap-2">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-white hover:bg-white/20 h-8 px-3"
                                        onClick={handleRefreshData}
                                        disabled={loading}
                                    >
                                        <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-white hover:bg-white/20 h-8 px-3"
                                        onClick={handleExportChart}
                                    >
                                        <Download className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="text-white hover:bg-white/20 h-8 px-3"
                                        onClick={() => setIsChartFullscreen(!isChartFullscreen)}
                                    >
                                        <Maximize2 className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="bg-white/10 border-white/20 text-white hover:bg-white/20 h-8 px-3"
                                        onClick={() => navigate('/dashboard/orders')}
                                    >
                                        View All
                                        <ArrowRight className="w-3 h-3 ml-1" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="p-6 bg-gradient-to-br from-white/90 to-slate-50/90 backdrop-blur-sm">
                        <div className={`${isChartFullscreen ? 'h-[600px]' : 'h-[400px]'} transition-all duration-300`}>
                            {loading ? (
                                <div className="w-full h-full flex items-center justify-center">
                                    <div className="text-center">
                                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                        <p className="text-slate-600">Loading analytics...</p>
                                    </div>
                                </div>
                            ) : stats.monthlyTrends.length === 0 ? (
                                <div className="w-full h-full flex items-center justify-center text-slate-500">
                                    <div className="text-center">
                                        <TrendingUp className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                                        <h3 className="text-lg font-semibold text-slate-600 mb-2">No Data Available</h3>
                                        <p className="text-slate-500">Start processing orders to see analytics</p>
                                    </div>
                                </div>
                            ) : (
                                <ResponsiveContainer width="100%" height="100%">
                                    {chartType === 'area' && (
                                        <AreaChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                            <defs>
                                                <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                                                    <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                                                    <stop offset="95%" stopColor="#10B981" stopOpacity={0.1}/>
                                                </linearGradient>
                                                <linearGradient id="colorOrders" x1="0" y1="0" x2="0" y2="1">
                                                    <stop offset="5%" stopColor="#1E40AF" stopOpacity={0.8}/>
                                                    <stop offset="95%" stopColor="#1E40AF" stopOpacity={0.1}/>
                                                </linearGradient>
                                            </defs>
                                            <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                            <XAxis
                                                dataKey="month"
                                                tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                interval="preserveStartEnd"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="left"
                                                orientation="left"
                                                allowDecimals={false}
                                                domain={[0, 'auto']}
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="right"
                                                orientation="right"
                                                domain={[0, 'auto']}
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <Tooltip
                                                labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                formatter={(value, name) => [
                                                    name === 'Revenue' ? `$${value.toFixed(2)}` : `${value} orders`,
                                                    name
                                                ]}
                                                contentStyle={{
                                                    backgroundColor: 'white',
                                                    border: '1px solid #e2e8f0',
                                                    borderRadius: '12px',
                                                    padding: '16px',
                                                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                                                }}
                                            />
                                            <Legend
                                                wrapperStyle={{
                                                    paddingTop: '20px',
                                                    fontSize: '14px',
                                                    color: '#64748B'
                                                }}
                                            />
                                            <Area
                                                yAxisId="left"
                                                type="monotone"
                                                dataKey="count"
                                                name="Orders"
                                                stroke="#1E40AF"
                                                strokeWidth={3}
                                                fillOpacity={1}
                                                fill="url(#colorOrders)"
                                                dot={{ r: 5, strokeWidth: 2, fill: '#1E40AF' }}
                                                activeDot={{ r: 8, stroke: '#1E40AF', strokeWidth: 2 }}
                                            />
                                            <Area
                                                yAxisId="right"
                                                type="monotone"
                                                dataKey="revenue"
                                                name="Revenue"
                                                stroke="#10B981"
                                                strokeWidth={3}
                                                fillOpacity={1}
                                                fill="url(#colorRevenue)"
                                                dot={{ r: 5, strokeWidth: 2, fill: '#10B981' }}
                                                activeDot={{ r: 8, stroke: '#10B981', strokeWidth: 2 }}
                                            />
                                        </AreaChart>
                                    )}

                                    {chartType === 'line' && (
                                        <LineChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                            <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                            <XAxis
                                                dataKey="month"
                                                tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                interval="preserveStartEnd"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="left"
                                                orientation="left"
                                                stroke="#1E40AF"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="right"
                                                orientation="right"
                                                stroke="#10B981"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <Tooltip
                                                labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                formatter={(value, name) => [
                                                    name === 'Revenue' ? `$${value.toFixed(2)}` : `${value} orders`,
                                                    name
                                                ]}
                                                contentStyle={{
                                                    backgroundColor: 'white',
                                                    border: '1px solid #e2e8f0',
                                                    borderRadius: '12px',
                                                    padding: '16px',
                                                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                                                }}
                                            />
                                            <Legend />
                                            <Line
                                                yAxisId="left"
                                                type="monotone"
                                                dataKey="count"
                                                name="Orders"
                                                stroke="#1E40AF"
                                                strokeWidth={3}
                                                dot={{ r: 5, fill: '#1E40AF' }}
                                                activeDot={{ r: 8 }}
                                            />
                                            <Line
                                                yAxisId="right"
                                                type="monotone"
                                                dataKey="revenue"
                                                name="Revenue"
                                                stroke="#10B981"
                                                strokeWidth={3}
                                                dot={{ r: 5, fill: '#10B981' }}
                                                activeDot={{ r: 8 }}
                                            />
                                        </LineChart>
                                    )}

                                    {chartType === 'bar' && (
                                        <BarChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                            <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                            <XAxis
                                                dataKey="month"
                                                tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                interval="preserveStartEnd"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="left"
                                                orientation="left"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="right"
                                                orientation="right"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <Tooltip
                                                labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                formatter={(value, name) => [
                                                    name === 'Revenue' ? `$${value.toFixed(2)}` : `${value} orders`,
                                                    name
                                                ]}
                                                contentStyle={{
                                                    backgroundColor: 'white',
                                                    border: '1px solid #e2e8f0',
                                                    borderRadius: '12px',
                                                    padding: '16px',
                                                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                                                }}
                                            />
                                            <Legend />
                                            <Bar
                                                yAxisId="left"
                                                dataKey="count"
                                                name="Orders"
                                                fill="#1E40AF"
                                                radius={[4, 4, 0, 0]}
                                            />
                                            <Bar
                                                yAxisId="right"
                                                dataKey="revenue"
                                                name="Revenue"
                                                fill="#10B981"
                                                radius={[4, 4, 0, 0]}
                                            />
                                        </BarChart>
                                    )}

                                    {chartType === 'composed' && (
                                        <ComposedChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                            <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                            <XAxis
                                                dataKey="month"
                                                tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                interval="preserveStartEnd"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="left"
                                                orientation="left"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <YAxis
                                                yAxisId="right"
                                                orientation="right"
                                                style={{ fontSize: '12px', fill: '#64748B' }}
                                            />
                                            <Tooltip
                                                labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                formatter={(value, name) => [
                                                    name === 'Revenue' ? `$${value.toFixed(2)}` : `${value} orders`,
                                                    name
                                                ]}
                                                contentStyle={{
                                                    backgroundColor: 'white',
                                                    border: '1px solid #e2e8f0',
                                                    borderRadius: '12px',
                                                    padding: '16px',
                                                    boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                                                }}
                                            />
                                            <Legend />
                                            <Bar
                                                yAxisId="left"
                                                dataKey="count"
                                                name="Orders"
                                                fill="#1E40AF"
                                                radius={[4, 4, 0, 0]}
                                            />
                                            <Line
                                                yAxisId="right"
                                                type="monotone"
                                                dataKey="revenue"
                                                name="Revenue"
                                                stroke="#10B981"
                                                strokeWidth={3}
                                                dot={{ r: 5, fill: '#10B981' }}
                                                activeDot={{ r: 8 }}
                                            />
                                        </ComposedChart>
                                    )}
                                </ResponsiveContainer>
                            )}
                        </div>
                    </div>
                </Card>
            </motion.div>

            {/* Recent Orders */}
            <Card className="shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden bg-white/50 backdrop-blur-sm">
                <div className="bg-gradient-to-r from-emerald-100 to-green-100 p-6 border-b">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-lg font-bold text-slate-800">Recent Orders</h2>
                            <p className="text-sm text-slate-600">Your latest eSIM purchases</p>
                        </div>
                        <Button 
                            variant="outline" 
                            className="bg-gradient-to-r from-emerald-500 to-green-500 text-white hover:from-emerald-600 hover:to-green-600 transition-all duration-300"
                            onClick={() => navigate('/dashboard/orders')}
                        >
                            View All
                            <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                    </div>
                </div>
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                <TableHead className="font-semibold text-slate-700">Order ID</TableHead>
                                <TableHead className="font-semibold text-slate-700">Plan</TableHead>
                                <TableHead className="font-semibold text-slate-700">Details</TableHead>
                                <TableHead className="font-semibold text-slate-700">Quantity</TableHead>
                                <TableHead className="font-semibold text-slate-700">Amount</TableHead>
                                <TableHead className="font-semibold text-slate-700">Status</TableHead>
                                <TableHead className="font-semibold text-slate-700">Date</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                Array(5).fill(0).map((_, i) => (
                                    <TableRow key={i} className="hover:bg-slate-50/80 transition-colors">
                                        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                stats.recentOrders.map((order) => (
                                    <TableRow key={order.id} className="hover:bg-slate-50/80 cursor-pointer transition-all duration-300">
                                        <TableCell className="font-medium text-slate-800">{order.id}</TableCell>
                                        <TableCell>{order.planName}</TableCell>
                                        <TableCell>
                                            <p className="flex items-center">
                                                {order.planType === 'Unlimited' ? (
                                                    <span className="font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full text-xs">
                                                        Unlimited
                                                    </span>
                                                ) : order.planType === 'Custom' ? (
                                                    <span className="font-medium text-violet-600 bg-violet-50 px-3 py-1 rounded-full text-xs">
                                                        {order.customPlanData || 'Custom Plan'}
                                                    </span>
                                                ) : order.planData && order.planDataUnit ? (
                                                    <span className="font-medium text-slate-600 bg-slate-100 px-3 py-1 rounded-full text-xs">
                                                        {order.planData} {order.planDataUnit}
                                                    </span>
                                                ) : (
                                                    '-'
                                                )}
                                                <span className="ml-2 text-slate-500 text-xs">
                                                    {order.validityDays} Day{order.validityDays > 1 ? 's' : ''}
                                                </span>
                                            </p>
                                        </TableCell>
                                        <TableCell className="font-medium">{order.quantity}</TableCell>
                                        <TableCell className="font-semibold text-slate-800">${order.amount?.toFixed(2)}</TableCell>
                                        <TableCell>
                                            <span className={`px-3 py-1 rounded-full text-xs ${
                                                order.status === 'completed' ? 'bg-emerald-100 text-emerald-800' :
                                                order.status === 'pending' ? 'bg-amber-100 text-amber-800' :
                                                'bg-slate-100 text-slate-800'
                                            }`}>
                                                {order.status}
                                            </span>
                                        </TableCell>
                                        <TableCell className="text-slate-600">
                                            {format(new Date(order.createdAt), 'MMM d, yyyy')}
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </Card>
        </div>
    );
}
