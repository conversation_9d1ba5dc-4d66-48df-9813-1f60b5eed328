import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { 
    Wallet, 
    ShoppingBag,
    ArrowRight,
    Signal,
    Globe,
    Zap,
    TrendingUp,
    ChevronRight,
    ChevronLeft
} from 'lucide-react';
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    Area,
    AreaChart
} from 'recharts';
import api from '@/lib/axios';

// Enhanced StatCard with modern design
const StatCard = ({ title, value, icon: Icon, trend, onClick, loading, description, color }) => {
    // Define gradient colors for each card type
    const gradients = {
        purple: 'from-purple-500 to-indigo-600',
        blue: 'from-blue-500 to-cyan-600',
        green: 'from-emerald-500 to-teal-600',
        orange: 'from-orange-500 to-amber-600',
    };

    const selectedGradient = gradients[color] || gradients.blue;

    return (
        <Card className="overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer">
            <div className={`bg-gradient-to-br ${selectedGradient} p-6 text-white h-full`} onClick={onClick}>
                <div className="flex items-start justify-between">
                    <div>
                        <p className="text-sm font-medium text-white/90">{title}</p>
                        {loading ? (
                            <Skeleton className="h-10 w-28 mt-2 bg-white/30" />
                        ) : (
                            <>
                                <h3 className="text-3xl font-bold mt-2">{value}</h3>
                                {description && (
                                    <p className="text-sm text-white/80 mt-1">{description}</p>
                                )}
                                {trend && (
                                    <div className="flex items-center mt-3 text-white/90">
                                        {trend.type === 'up' ? (
                                            <TrendingUp className="w-4 h-4 mr-1" />
                                        ) : (
                                            <TrendingUp className="w-4 h-4 mr-1 transform rotate-180" />
                                        )}
                                        <p className="text-sm font-medium">{trend.value}</p>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                    <div className="bg-white/20 p-3 rounded-xl backdrop-blur-sm">
                        <Icon className="w-6 h-6" />
                    </div>
                </div>
            </div>
        </Card>
    );
};

export default function Dashboard() {
    const navigate = useNavigate();
    const { toast } = useToast();
    const [loading, setLoading] = useState(true);
    const [stats, setStats] = useState({
        walletBalance: 0,
        totalOrders: 0,
        totalPlans: 0,
        last30DaysOrders: 0,
        monthlyTrends: [],
        recentOrders: []
    });

    useEffect(() => {
        fetchDashboardStats();
    }, []);

    const fetchDashboardStats = async () => {
        try {
            setLoading(true);
            const response = await api.get('/api/partner/dashboard/stats');
            setStats(response.data);
        } catch (error) {
            // console.error('Error fetching dashboard stats:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch dashboard statistics"
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="h-full flex flex-col gap-6 bg-gradient-to-br from-slate-50 to-gray-100 p-6 rounded-lg">
            <div className="bg-gradient-to-r from-blue-800 to-blue-600 p-6 rounded-xl shadow-sm">
                <h1 className="text-2xl font-bold text-white">Partner Dashboard</h1>
                <p className="text-slate-600 mt-1 text-white">Welcome back! Here's an overview of your eSIM services</p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                    title="Wallet Balance"
                    value={`$${stats.walletBalance.toFixed(2)}`}
                    icon={Wallet}
                    description="Available funds for purchases"
                    onClick={() => navigate('/dashboard/wallet')}
                    loading={loading}
                    color="purple"
                />
                <StatCard
                    title="Total Orders"
                    value={stats.totalOrders}
                    icon={ShoppingBag}
                    description="Lifetime eSIM purchases"
                    trend={{ 
                        type: 'up', 
                        value: `${stats.last30DaysOrders} new this month` 
                    }}
                    onClick={() => navigate('/dashboard/orders')}
                    loading={loading}
                    color="blue"
                />
                <StatCard
                    title="Available Plans"
                    value={stats.totalPlans}
                    icon={Globe}
                    description="Active eSIM plans"
                    onClick={() => navigate('/dashboard/plans')}
                    loading={loading}
                    color="green"
                />
                <StatCard
                    title="Active Services"
                    value={stats.last30DaysOrders}
                    icon={Signal}
                    description="Orders in last 30 days"
                    onClick={() => navigate('/dashboard/orders')}
                    loading={loading}
                    color="orange"
                />
            </div>

            {/* Monthly Trends Chart */}
            <Card className="shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden bg-white/50 backdrop-blur-sm">
                <div className="bg-gradient-to-r from-sky-100 to-blue-100 p-6 border-b">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-lg font-bold text-slate-800">Order History</h2>
                            <p className="text-sm text-slate-600">All-time eSIM purchase history by month</p>
                        </div>
                        <Button 
                            variant="outline" 
                            className="bg-gradient-to-r from-sky-500 to-blue-500 text-white hover:from-sky-600 hover:to-blue-600 transition-all duration-300"
                            onClick={() => navigate('/dashboard/orders')}
                        >
                            View All Orders
                            <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                    </div>
                </div>
                <div className="p-6 bg-white/80">
                    <div className="h-[300px]">
                        {loading ? (
                            <div className="w-full h-full flex items-center justify-center">
                                <Skeleton className="w-full h-full" />
                            </div>
                        ) : stats.monthlyTrends.length === 0 ? (
                            <div className="w-full h-full flex items-center justify-center text-slate-500">
                                <div className="text-center">
                                    <TrendingUp className="w-12 h-12 text-slate-300 mx-auto mb-3" />
                                    <p>No orders found</p>
                                </div>
                            </div>
                        ) : (
                            <ResponsiveContainer width="100%" height="100%">
                                <AreaChart data={stats.monthlyTrends}>
                                    <defs>
                                        <linearGradient id="colorOrders" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="5%" stopColor="#6366F1" stopOpacity={0.8}/>
                                            <stop offset="95%" stopColor="#6366F1" stopOpacity={0}/>
                                        </linearGradient>
                                    </defs>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
                                    <XAxis 
                                        dataKey="month" 
                                        tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                        interval="preserveStartEnd"
                                        style={{ fontSize: '12px', fill: '#64748B' }}
                                    />
                                    <YAxis 
                                        allowDecimals={false}
                                        domain={[0, 'auto']}
                                        style={{ fontSize: '12px', fill: '#64748B' }}
                                    />
                                    <Tooltip 
                                        labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                        formatter={(value) => [`${value} orders`, 'Orders']}
                                        contentStyle={{
                                            backgroundColor: 'white',
                                            border: '1px solid #e2e8f0',
                                            borderRadius: '12px',
                                            padding: '12px',
                                            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                                        }}
                                    />
                                    <Legend 
                                        wrapperStyle={{ 
                                            paddingTop: '12px', 
                                            fontSize: '12px',
                                            color: '#64748B'
                                        }} 
                                    />
                                    <Area
                                        type="monotone"
                                        dataKey="count"
                                        name="Orders"
                                        stroke="#6366F1"
                                        strokeWidth={2}
                                        fillOpacity={1}
                                        fill="url(#colorOrders)"
                                        dot={{ r: 4, strokeWidth: 2 }}
                                        activeDot={{ r: 8 }}
                                    />
                                </AreaChart>
                            </ResponsiveContainer>
                        )}
                    </div>
                </div>
            </Card>

            {/* Recent Orders */}
            <Card className="shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden bg-white/50 backdrop-blur-sm">
                <div className="bg-gradient-to-r from-emerald-100 to-green-100 p-6 border-b">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-lg font-bold text-slate-800">Recent Orders</h2>
                            <p className="text-sm text-slate-600">Your latest eSIM purchases</p>
                        </div>
                        <Button 
                            variant="outline" 
                            className="bg-gradient-to-r from-emerald-500 to-green-500 text-white hover:from-emerald-600 hover:to-green-600 transition-all duration-300"
                            onClick={() => navigate('/dashboard/orders')}
                        >
                            View All
                            <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                    </div>
                </div>
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                <TableHead className="font-semibold text-slate-700">Order ID</TableHead>
                                <TableHead className="font-semibold text-slate-700">Plan</TableHead>
                                <TableHead className="font-semibold text-slate-700">Details</TableHead>
                                <TableHead className="font-semibold text-slate-700">Quantity</TableHead>
                                <TableHead className="font-semibold text-slate-700">Amount</TableHead>
                                <TableHead className="font-semibold text-slate-700">Status</TableHead>
                                <TableHead className="font-semibold text-slate-700">Date</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                Array(5).fill(0).map((_, i) => (
                                    <TableRow key={i} className="hover:bg-slate-50/80 transition-colors">
                                        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                        <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                stats.recentOrders.map((order) => (
                                    <TableRow key={order.id} className="hover:bg-slate-50/80 cursor-pointer transition-all duration-300">
                                        <TableCell className="font-medium text-slate-800">{order.id}</TableCell>
                                        <TableCell>{order.planName}</TableCell>
                                        <TableCell>
                                            <p className="flex items-center">
                                                {order.planType === 'Unlimited' ? (
                                                    <span className="font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full text-xs">
                                                        Unlimited
                                                    </span>
                                                ) : order.planType === 'Custom' ? (
                                                    <span className="font-medium text-violet-600 bg-violet-50 px-3 py-1 rounded-full text-xs">
                                                        {order.customPlanData || 'Custom Plan'}
                                                    </span>
                                                ) : order.planData && order.planDataUnit ? (
                                                    <span className="font-medium text-slate-600 bg-slate-100 px-3 py-1 rounded-full text-xs">
                                                        {order.planData} {order.planDataUnit}
                                                    </span>
                                                ) : (
                                                    '-'
                                                )}
                                                <span className="ml-2 text-slate-500 text-xs">
                                                    {order.validityDays} Day{order.validityDays > 1 ? 's' : ''}
                                                </span>
                                            </p>
                                        </TableCell>
                                        <TableCell className="font-medium">{order.quantity}</TableCell>
                                        <TableCell className="font-semibold text-slate-800">${order.amount?.toFixed(2)}</TableCell>
                                        <TableCell>
                                            <span className={`px-3 py-1 rounded-full text-xs ${
                                                order.status === 'completed' ? 'bg-emerald-100 text-emerald-800' :
                                                order.status === 'pending' ? 'bg-amber-100 text-amber-800' :
                                                'bg-slate-100 text-slate-800'
                                            }`}>
                                                {order.status}
                                            </span>
                                        </TableCell>
                                        <TableCell className="text-slate-600">
                                            {format(new Date(order.createdAt), 'MMM d, yyyy')}
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>
            </Card>
        </div>
    );
}
