const mobimatterService = require('./mobimatter.service');
const { v4: uuidv4 } = require('uuid');
const generateProductId = require('../utils/generateProductId');
const cacheService = require('./cache.service');

const descriptionCache = new Map();

class ProviderFactory {
    constructor() {
        this.providers = {
            'mobimatter': mobimatterService,
            // Add more providers here as they are integrated
        };
    }

    getProvider(providerType) {
        const provider = this.providers[providerType.toLowerCase()];
        if (!provider) {
            throw new Error(`Provider ${providerType} not supported`);
        }
        return provider;
    }

    // Convert provider product to our standard format
    async standardizeProduct(providerName, product) {
        if (!product) {
            throw new Error('Product data is required');
        }

        // Check cache first
        const cacheKey = `${product.id}_description`;
        if (descriptionCache.has(cacheKey)) {
            return descriptionCache.get(cacheKey);
        }

        // Rest of the standardization logic...
        const standardizedProduct = await this._standardizeMobimatterProduct(product);
        
        // Cache the result for 1 hour
        descriptionCache.set(cacheKey, standardizedProduct);
        setTimeout(() => descriptionCache.delete(cacheKey), 3600000);

        return standardizedProduct;
    }

    async _standardizeMobimatterProduct(product) {
        // Map product category to our system's category
        let category = 'esim_realtime';
        
        // Check product category and metadata for replacement plans
        if (product.category?.toLowerCase().includes('addon') || 
            product.providerMetadata?.productType?.toLowerCase().includes('addon')) {
            category = 'esim_addon';
        } else if (product.category?.toLowerCase().includes('replacement') || 
                 product.providerMetadata?.productType?.toLowerCase().includes('replacement') ||
                 product.name?.toLowerCase().includes('replacement')) {
            category = 'esim_replacement';
        }

        // Determine plan type based on unlimited flag
        const planType = product.isUnlimited || product.customData?.find(d => d.name === 'UNLIMITED')?.value === '1' ? 'Unlimited' : 'Fixed';

        // Format features as string array
        const features = product.features?.map(feature => 
            `${feature.name}: ${feature.value}`
        ) || [];

        // Extract key information from customData
        const planDetails = product.customData ? {
            title: product.customData.find(d => d.name === 'PLAN_TITLE')?.value,
            dataLimit: product.customData.find(d => d.name === 'PLAN_DATA_LIMIT')?.value,
            validity: product.customData.find(d => d.name === 'PLAN_VALIDITY')?.value,
            details: product.customData.find(d => d.name === 'PLAN_DETAILS')?.value,
            speed: product.customData.find(d => d.name === 'SPEED')?.value,
            speedLong: product.customData.find(d => d.name === 'SPEED_LONG')?.value,
            networks: product.customData.find(d => d.name === 'NETWORKS_SHORT')?.value,
            activationPolicy: product.customData.find(d => d.name === 'ACTIVATION_POLICY')?.value,
            additionalDetails: product.customData.find(d => d.name === 'ADDITIONAL_DETAILS')?.value,
            usageTracking: product.customData.find(d => d.name === 'USAGE_TRACKING')?.value,
            hasVoice: product.customData.find(d => d.name === 'HAS_CALLS')?.value === '1',
            hasData: product.customData.find(d => d.name === 'HAS_DATA')?.value === '1',
            hasSms: product.customData.find(d => d.name === 'HAS_SMS')?.value === '1',
            hotspot: product.customData.find(d => d.name === 'HOTSPOT')?.value === '1',
            fiveG: product.customData.find(d => d.name === 'FIVEG')?.value === '1',
            unlimited: product.customData.find(d => d.name === 'UNLIMITED')?.value === '1',
            top_up: product.customData.find(d => d.name === 'TOPUP')?.value === '1'
        } : {};

        // Format the important details separately
        let description = '';
        let planInfoContent = '';
        let additionalInfoContent = '';
        
        console.log('Processing description from:', {
            planDetails,
            productDetails: product.productDetails,
            customData: product.customData
        });

        // First try to get description from planDetails.details for the planInfo
        if (planDetails.details) {
            try {
                const detailsObj = JSON.parse(planDetails.details);
                console.log('Parsed planDetails.details:', detailsObj);
                if (detailsObj.description) {
                    description = detailsObj.description;
                    planInfoContent += `<div>${detailsObj.description}</div>`;
                    console.log('Found description in planDetails:', description);
                }
                if (detailsObj.items?.length) {
                    description += '\n\nKey Features:\n' + detailsObj.items.map(item => `• ${item}`).join('\n');
                    planInfoContent += `
                    <div class="mt-4">
                        <h4 class="text-sm font-semibold mb-2">Key Features:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            ${detailsObj.items.map(item => `<li>${item}</li>`).join('\n')}
                        </ul>
                    </div>`;
                }
            } catch (e) {
                console.error('Error parsing planDetails.details:', e);
                description = planDetails.details;
                planInfoContent = `<div>${planDetails.details}</div>`;
            }
        }

        // If description is still empty, try productDetails
        if ((!description || !planInfoContent) && product.productDetails?.length) {
            console.log('Trying productDetails:', product.productDetails);
            const planDetailsFromProduct = product.productDetails.find(d => d.name.trim() === 'PLAN_DETAILS');
            if (planDetailsFromProduct?.value) {
                try {
                    const detailsObj = JSON.parse(planDetailsFromProduct.value);
                    console.log('Parsed productDetails:', detailsObj);
                    if (detailsObj.heading) {
                        description = detailsObj.heading + '\n\n';
                        planInfoContent += `<h3 class="text-md font-semibold">${detailsObj.heading}</h3>`;
                    }
                    if (detailsObj.description) {
                        description += detailsObj.description + '\n\n';
                        planInfoContent += `<div class="mt-2">${detailsObj.description}</div>`;
                        console.log('Found description in productDetails:', description);
                    }
                    if (detailsObj.items?.length) {
                        description += 'Key Features:\n' + detailsObj.items.map(item => `• ${item}`).join('\n');
                        planInfoContent += `
                        <div class="mt-4">
                            <h4 class="text-sm font-semibold mb-2">Key Features:</h4>
                            <ul class="list-disc list-inside space-y-1">
                                ${detailsObj.items.map(item => `<li>${item}</li>`).join('\n')}
                            </ul>
                        </div>`;
                    }
                } catch (e) {
                    console.error('Error parsing productDetails:', e);
                    description = planDetailsFromProduct.value;
                    planInfoContent = `<div>${planDetailsFromProduct.value}</div>`;
                }
            }
        }

        // Add additional details if available to additionalInfoContent
        if (planDetails.additionalDetails) {
            const cleanDetails = planDetails.additionalDetails.replace(/<[^>]*>/g, '').trim();
            if (cleanDetails) {
                description += '\n\nAdditional Information:\n' + cleanDetails;
                additionalInfoContent += `
                <div class="mt-4">
                    <h4 class="text-sm font-semibold mb-2">Additional Information:</h4>
                    <p>${cleanDetails}</p>
                </div>`;
                console.log('Added additional details:', cleanDetails);
            }
        }

        // Add usage tracking to additionalInfoContent
        if (planDetails.usageTracking) {
            additionalInfoContent += `
            <div class="mt-4">
                <h4 class="text-sm font-semibold mb-2">Usage Tracking:</h4>
                <p>${planDetails.usageTracking}</p>
            </div>`;
        }

        // Add any other relevant information from productDetails to additionalInfoContent
        if (product.productDetails && Array.isArray(product.productDetails)) {
            const heading = product.productDetails.find(detail => detail.name === "heading")?.value;
            if (heading && !planInfoContent.includes(heading)) {
                additionalInfoContent += `
                <div class="mt-4">
                    <h4 class="text-sm font-semibold mb-2">Product Details:</h4>
                    <p>${heading}</p>
                </div>`;
            }
            
            // Add other product details that might be useful
            product.productDetails
                .filter(detail => 
                    detail.name !== "heading" && 
                    detail.name !== "PLAN_DATA_LIMIT" && 
                    detail.name !== "PLAN_DETAILS" &&
                    detail.value)
                .forEach(detail => {
                    additionalInfoContent += `
                    <div class="mt-2">
                        <strong>${detail.name}:</strong> ${detail.value}
                    </div>`;
                });
        }

        console.log('Final processed description:', description);
        console.log('Generated planInfo:', planInfoContent);
        console.log('Generated additionalInfo:', additionalInfoContent);

        // Store usage tracking separately in providerMetadata instead of adding to description
        const usageTracking = planDetails.usageTracking || null;

        // Get network type from NETWORKS_SHORT
        const networkType = (() => {
            const networkShort = product.customData?.find(d => d.name === 'NETWORKS_SHORT')?.value;
            if (networkShort) {
                return networkShort; // Use the exact value from Mobimatter
            }
            // Fallback to checking if 5G is supported
            return planDetails.fiveG ? '5G/4G/LTE' : '4G/LTE';
        })();

        // Set appropriate speed
        const speed = (() => {
            // Only mark as Restricted if speed is explicitly set to something other than Unrestricted
            // This matches the database schema where Unrestricted is the default
            return planDetails.speed && planDetails.speed !== 'Unrestricted' ? 'Restricted' : 'Unrestricted';
        })();

        // Set hotspot availability based on multiple possible indicators
        const hotspot = planDetails.hotspot === '1' || 
                      planDetails.hotspot === true ||
                      product.customData?.find(d => d.name === 'TETHERING')?.value === '1' ||
                      product.customData?.find(d => d.name === 'HOTSPOT')?.value === '1' ? 
                      'Available' : 'Not Available';

        // Set top_up availability based on multiple possible indicators
        const top_up = planDetails.top_up === '1' || 
                      planDetails.top_up === true ||
                      product.customData?.find(d => d.name === 'TOPUP')?.value === '1' ? 
                      'Available' : 'Not Available';

        // Set SMS availability based on HAS_SMS flag
        const is_sms = product.hasSms || product.customData?.find(d => d.name === 'HAS_SMS')?.value === '1' ? 
                      'Available' : 'Not Available';

        // Set voice availability based on HAS_CALLS or PLAN_VOICE_LIMIT
        const is_voice = product.hasVoice ||
            product.customData?.find(d => d.name === 'HAS_CALLS')?.value === '1' ||
            parseInt(product.customData?.find(d => d.name === 'PLAN_VOICE_LIMIT')?.value || '0') > 0
            ? 'Available'
            : 'Not Available';


        // Ensure we have a valid name
        const name = planDetails.title || product.name || product.productFamilyName || 'Unknown Plan';

        // Parse data amount from PLAN_DATA_LIMIT
        let planData = null;
        let planDataUnit = null;
        if (planDetails.dataLimit) {
            planData = parseFloat(planDetails.dataLimit);
            planDataUnit = 'GB';
        }

        // Ensure we have valid validity days
        const validityDays = (() => {
            // Check if validity is in hours (Mobimatter case)
            const validityInHours = parseInt(planDetails.validity);
            if (!isNaN(validityInHours)) {
                // Convert hours to days, rounding up to the nearest day
                return Math.ceil(validityInHours / 24);
            }
            // Fallback to direct validity days if available, or default to 30
            return parseInt(product.validityDays) || 30;
        })();

        // Format regions and countries with caching
        const regions = await this._getCachedRegions(product);
        const supportedCountries = this._getCachedCountries(product);

        // Ensure we have valid IDs
        const externalProductId = product.id || product.uniqueId || product.productId;
        const externalSkuId = product.skuId || product.uniqueId || externalProductId;

        if (!externalProductId) {
            throw new Error('Product must have an ID');
        }

        // Extract voice minutes if available
        let voiceMin = null;
        let voiceMinUnit = null;
        let planCategory = 'Data Only';
        
        // Check for voice minutes in the product
        if (product.voiceMinutes) {
            voiceMin = product.voiceMinutes;
            voiceMinUnit = 'Min';
            planCategory = 'Voice and Data';
            console.log('Set Voice and Data from product voiceMinutes:', { voiceMin, voiceMinUnit, planCategory });
        } else {
            // Check for PLAN_VOICE_LIMIT in metadata
            const voiceLimit = product.customData?.find(data => data.name === 'PLAN_VOICE_LIMIT')?.value;
            const hasCalls = product.customData?.find(data => data.name === 'HAS_CALLS')?.value === '1';
            
            console.log('Voice detection:', {
                voiceLimit,
                hasCalls,
                customData: product.customData
            });
            
            if (voiceLimit) {
                // Directly parse the voice limit value as a number
                voiceMin = parseInt(voiceLimit);
                if (!isNaN(voiceMin) && voiceMin > 0) {
                    voiceMinUnit = 'Min';
                    planCategory = 'Voice and Data';
                    console.log('Set Voice and Data from PLAN_VOICE_LIMIT:', { voiceMin, voiceMinUnit, planCategory });
                }
            } else if (hasCalls) {
                // Try to extract voice minutes from plan details
                const planDetails = product.customData?.find(data => data.name === 'PLAN_DETAILS')?.value;
                if (planDetails) {
                    try {
                        const details = JSON.parse(planDetails);
                        if (details.items) {
                            const voiceItem = details.items.find(item => 
                                item.toLowerCase().includes('voice') || 
                                item.toLowerCase().includes('minutes')
                            );
                            if (voiceItem) {
                                const match = voiceItem.match(/(\d+)\s*(min|minutes)/i);
                                if (match) {
                                    voiceMin = parseInt(match[1]);
                                    voiceMinUnit = 'Min';
                                    planCategory = 'Voice and Data';
                                    console.log('Set Voice and Data from plan details:', { voiceMin, voiceMinUnit, planCategory });
                                } else {
                                    // Voice is available but no specific minutes found
                                    planCategory = 'Voice and Data';
                                    console.log('Set Voice and Data without minutes:', { voiceMin, voiceMinUnit, planCategory });
                                }
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing plan details:', e);
                    }
                }
                
                // If HAS_CALLS is true but we haven't found voice minutes, just set category
                if (!voiceMin) {
                    planCategory = 'Voice and Data';
                    console.log('Set Voice and Data from HAS_CALLS flag:', { voiceMin, voiceMinUnit, planCategory });
                }
            }
        }

        // Ensure we have a valid network name
        const networkName = product.networkName || product.providerName || 'Unknown Network';

        // Ensure we have a valid buying price
        const buyingPrice = parseFloat(product.price || product.wholesalePrice) || 0;

        // Set activation policy with more detailed conditions
        const activationPolicy = planDetails.activationPolicy?.toLowerCase().includes('first usage') ||
                              planDetails.activationPolicy?.toLowerCase().includes('connecting to network') ||
                              product.customData?.find(d => 
                                  d.name === 'ACTIVATION_POLICY' && 
                                  (d.value.toLowerCase().includes('first usage') || 
                                   d.value.toLowerCase().includes('connecting to network'))
                              ) ? 'Activation upon first usage' :
                              planDetails.activationPolicy?.toLowerCase().includes('travel date') ? 
                              'Activation upon travel date' : 'Activation upon purchase';

        // Create the standardized plan object with all required fields
        const standardizedProduct = {
            externalProductId,
            externalSkuId,
            name,
            description,
            planInfo: planInfoContent,
            additionalInfo: additionalInfoContent,
            networkName,
            networkType,
            region: regions.join(', '),
            supportedRegions: regions,
            supportedCountries,
            buyingPrice,
            sellingPrice: null,
            validityDays,
            planType,
            planCategory,
            planData,
            planDataUnit,
            category,
            status: 'visible',
            isActive: true,
            startDateEnabled: false,
            activationPolicy,
            features: [],
            voiceMin,
            voiceMinUnit,
            speed,
            hotspot,
            top_up,
            is_sms,
            is_voice,
            stockThreshold: 10,
            profile: 'local',
            providerMetadata: {
                ...product.providerMetadata,
                originalData: product,
                customData: product.customData,
                displayAttributes: product.providerMetadata?.displayAttributes || [],
                productDetails: product.productDetails || [],
                usageTracking
            }
        };

        return standardizedProduct;
    }

    // Helper method to get cached regions
    async _getCachedRegions(product) {
        const cacheKey = `${cacheService.KEYS.REGIONS}_${product.id}`;
        
        // Try to get from cache first
        let regions = cacheService.get(cacheKey);
        
        if (!regions) {
            // Get country codes from the product
            const countryCodes = Array.isArray(product.supportedCountries) ? 
                product.supportedCountries : 
                (product.region ? [product.region] : []);

            if (countryCodes.length === 0) {
                return ['Global'];
            }

            try {
                // Get regions for these countries from the database
                const { Country } = require('../models');
                const { Op } = require('sequelize');

                const countries = await Country.findAll({
                    attributes: ['region'],
                    where: {
                        id: {
                            [Op.in]: countryCodes.map(code => code.toUpperCase())
                        },
                        region: {
                            [Op.not]: null
                        }
                    },
                    raw: true
                });

                // Extract unique regions
                regions = [...new Set(countries.map(country => country.region))];

                if (regions.length === 0) {
                    regions = ['Global'];
                }

                // Cache for 1 hour
                cacheService.set(cacheKey, regions, 3600);
            } catch (error) {
                console.error('Error mapping countries to regions:', error);
                regions = ['Global'];
            }
        }
        
        return regions;
    }

    // Helper method to get cached countries
    _getCachedCountries(product) {
        const cacheKey = `${cacheService.KEYS.COUNTRIES}_${product.id}`;
        
        // Try to get from cache first
        let countries = cacheService.get(cacheKey);
        
        if (!countries) {
            // If not in cache, use product's supported countries or empty array
            countries = product.supportedCountries || [];
            
            // Cache for 1 hour
            cacheService.set(cacheKey, countries);
        }
        
        return countries;
    }

    // Helper method to determine plan type
    _determinePlanType(product) {
        if (product.isUnlimited || product.dataAmount === -1) return 'Unlimited';
        if (product.customData) return 'Custom';
        return 'Fixed';
    }

    // Convert provider order to our standard format
    standardizeOrder(providerType, order) {
        switch (providerType.toLowerCase()) {
            case 'mobimatter':
                return {
                    externalOrderId: order.id,
                    status: this._mapOrderStatus(order.status),
                    providerResponse: order,
                    providerMetadata: {
                        activationCode: order.activationCode,
                        qrCodeUrl: order.qrCodeUrl,
                        iccid: order.iccid,
                        providerStatus: order.status
                    },
                    usageData: order.usage || null,
                    providerOrderStatus: order.status,
                    providerErrorCode: order.errorCode,
                    providerErrorMessage: order.errorMessage
                };
            default:
                throw new Error(`Provider ${providerType} not supported`);
        }
    }

    // Map provider-specific order status to our standard status
    _mapOrderStatus(providerStatus) {
        const statusMap = {
            'mobimatter': {
                'PENDING': 'pending',
                'COMPLETED': 'completed',
                'FAILED': 'failed',
                'CANCELLED': 'cancelled'
            }
        };

        return statusMap.mobimatter[providerStatus] || 'pending';
    }
}

module.exports = new ProviderFactory(); 